import { FORMATTING_CONSTANTS } from '@/constants/formatting';

export const formatters = {
  price: (value?: number, currency = 'USD'): string => {
    if (value === undefined || value === null) {
      return '-';
    }

    return (
      (currency === 'USD' ? '$' : ' ') +
      value.toLocaleString('en-US', {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits:
          currency === 'BTC'
            ? FORMATTING_CONSTANTS.PRICE_DECIMAL_PLACES_HIGH
            : FORMATTING_CONSTANTS.PRICE_DECIMAL_PLACES_LOW,
      }) +
      (currency === 'BTC' ? ' BTC' : ' ')
    );
  },

  marketCap: (value?: number): string => {
    if (
      value === null ||
      value === undefined ||
      Number.isNaN(value) ||
      value === 0
    ) {
      return '-';
    }

    const suffixes = [
      { threshold: 1e12, suffix: 'T' },
      { threshold: 1e9, suffix: 'B' },
      { threshold: 1e6, suffix: 'M' },
      { threshold: 1e3, suffix: 'K' },
    ];

    for (const { threshold, suffix } of suffixes) {
      if (value >= threshold) {
        return `$${(value / threshold).toLocaleString('en-US', {
          maximumFractionDigits: 2,
          minimumFractionDigits: 1,
        })}${suffix}`;
      }
    }

    return `${value.toLocaleString('en-US', { maximumFractionDigits: 0 })}`;
  },

  technicalIndicator: (value?: number): string => {
    if (value === undefined || value === null) {
      return '-';
    }

    return value.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 6,
    });
  },

  percentage: (value?: number): string => {
    if (value === undefined || value === null) {
      return '-';
    }

    const sign = value >= 0 ? '+' : '';
    return `${sign}${value.toFixed(2)}%`;
  },

  volume: (value?: number): string => {
    if (
      value === null ||
      value === undefined ||
      Number.isNaN(value) ||
      value === 0
    ) {
      return '-';
    }

    const suffixes = [
      { threshold: 1e9, suffix: 'B' },
      { threshold: 1e6, suffix: 'M' },
      { threshold: 1e3, suffix: 'K' },
    ];

    for (const { threshold, suffix } of suffixes) {
      if (value >= threshold) {
        return `${(value / threshold).toLocaleString('en-US', {
          maximumFractionDigits: 1,
        })}${suffix}`;
      }
    }

    return value.toLocaleString('en-US', { maximumFractionDigits: 0 });
  },
};

export const navigation = {
  openCoinMarketCap: (slug?: string): void => {
    if (slug === null || slug === undefined || slug === '') {
      return;
    }

    const url = `https://coinmarketcap.com/currencies/${slug}/`;
    window.open(url, '_blank', 'noopener,noreferrer');
  },
  openYahoo: (symbol: string): void => {
    if (symbol === null || symbol === undefined || symbol === '') {
      return;
    }

    const url = `https://finance.yahoo.com/quote/${symbol}`;
    window.open(url, '_blank', 'noopener,noreferrer');
  },
};
